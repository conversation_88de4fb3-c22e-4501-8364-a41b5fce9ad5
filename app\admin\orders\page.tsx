"use client";

import { useState, useEffect } from "react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { useRouter } from "next/navigation";
import Image from "next/image";
import type { Order } from "@/types/orders";

// Type for product info that can come from either products relation or product_snapshot
type ProductInfo = {
  id?: string;
  name?: string;
  image_url?: string;
  image?: string;
};
import OrderDetails from "@/components/admin/OrderDetails";

export default function AdminOrdersPage() {
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const supabase = createClientComponentClient();

  useEffect(() => {
    const checkAuth = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session) {
        router.push("/sign-in");
        return;
      }

      // Get user's role
      const { data: profile } = await supabase
        .from("profiles")
        .select("role")
        .eq("id", session.user.id)
        .single();

      if (!profile || profile.role !== "admin") {
        router.push("/");
        return;
      }

      fetchOrders();
    };

    checkAuth();
  }, []);

  const fetchOrders = async () => {
    try {
      const { data: ordersData } = await supabase
        .from("orders")
        .select(
          `
          id,
          created_at,
          total_amount,
          status,
          shipping_address,
          order_items (
            quantity,
            unit_price,
            size,
            color,
            fabric,
            product_snapshot,
            products (
              id,
              name,
              image_url
            )
          )
        `
        )
        .order("created_at", { ascending: false });

      if (ordersData) {
        setOrders(ordersData as unknown[] as Order[]);
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleOrderClick = (order: Order) => {
    setSelectedOrder(order);
  };

  const handleOrderUpdate = () => {
    fetchOrders();
    setSelectedOrder(null);
    router.refresh();
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 w-48 bg-gray-200"></div>
          <div className="mt-8 space-y-4">
            <div className="h-12 rounded bg-gray-200"></div>
            <div className="h-12 rounded bg-gray-200"></div>
            <div className="h-12 rounded bg-gray-200"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-8 text-2xl font-bold">Order Management</h1>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Order ID
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Items
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Total
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Status
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {orders?.map((order) => (
              <tr
                key={order.id}
                onClick={() => handleOrderClick(order)}
                className="cursor-pointer hover:bg-gray-50"
              >
                <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                  #{order.id.slice(0, 8)}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                  {new Date(order.created_at).toLocaleDateString()}
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    <div className="flex -space-x-2">
                      {order.order_items?.map((item, index) => {
                        // Get product info from either products relation or product_snapshot
                        const productInfo = (item.products || item.product_snapshot || {}) as ProductInfo;
                        const productId = productInfo.id || `snapshot-${index}`;
                        const productName = productInfo.name || 'Unknown Product';
                        const productImage = productInfo.image_url || productInfo.image || '/placeholder-product.jpg';

                        return (
                          <div
                            key={`${order.id}-${productId}`}
                            className="relative h-8 w-8 overflow-hidden rounded-full border-2 border-white"
                          >
                            <Image
                              src={productImage}
                              alt={productName}
                              fill
                              className="object-cover"
                            />
                          </div>
                        );
                      })}
                    </div>
                    <span className="text-sm text-gray-500">
                      {order.order_items?.length} items
                    </span>
                  </div>
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                  ₹{(order.total_amount || 0).toFixed(2)}
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <span
                    className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                      order.status === "delivered"
                        ? "bg-blue-100 text-blue-800"
                        : order.status === "paid"
                        ? "bg-green-100 text-green-800"
                        : "bg-yellow-100 text-yellow-800"
                    }`}
                  >
                    {order.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {selectedOrder && (
        <OrderDetails
          order={selectedOrder}
          onUpdate={handleOrderUpdate}
          onClose={() => setSelectedOrder(null)}
        />
      )}
    </div>
  );
}
