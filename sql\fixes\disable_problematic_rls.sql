-- Quick fix: Disable problematic RLS and triggers
-- This is a simple solution to immediately fix the "permission denied for table users" error

-- Drop all problematic triggers that access auth.users
DROP TRIGGER IF EXISTS create_cancellation_notification_trigger ON public.cancellation_requests;
DROP TRIGGER IF EXISTS create_cancellation_notification_simple_trigger ON public.cancellation_requests;
DROP FUNCTION IF EXISTS create_cancellation_notification();
DROP FUNCTION IF EXISTS create_cancellation_notification_simple();

-- Drop all problematic RLS policies that access auth.users
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage all cancellation requests" ON public.cancellation_requests;
DROP POLICY IF EXISTS "Ad<PERSON> can manage cancellation notifications" ON public.cancellation_notifications;
DROP POLICY IF EXISTS "Users can manage their own cancellation requests" ON public.cancellation_requests;

-- Disable RLS entirely for cancellation tables (simplest fix)
ALTER TABLE public.cancellation_requests DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.cancellation_notifications DISABLE ROW LEVEL SECURITY;

-- Optional: Create very simple policies that don't access auth.users
-- Uncomment these if you want some basic security but without auth.users access

-- ALTER TABLE public.cancellation_requests ENABLE ROW LEVEL SECURITY;
-- CREATE POLICY "Allow authenticated users" ON public.cancellation_requests
--   FOR ALL USING (auth.uid() IS NOT NULL);

-- ALTER TABLE public.cancellation_notifications ENABLE ROW LEVEL SECURITY;  
-- CREATE POLICY "Allow authenticated users" ON public.cancellation_notifications
--   FOR ALL USING (auth.uid() IS NOT NULL);

-- Success message
DO $$
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '✅ Quick fix applied successfully!';
  RAISE NOTICE '';
  RAISE NOTICE 'Changes made:';
  RAISE NOTICE '• Removed all triggers that access auth.users table';
  RAISE NOTICE '• Removed all RLS policies that access auth.users table';
  RAISE NOTICE '• Disabled RLS on cancellation tables';
  RAISE NOTICE '';
  RAISE NOTICE 'Security note: RLS is now disabled for cancellation tables.';
  RAISE NOTICE 'This is safe for development but consider re-enabling with proper policies for production.';
  RAISE NOTICE '';
  RAISE NOTICE 'The cancellation system should now work without permission errors!';
END $$;
