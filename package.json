{"name": "e-commerce-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "lucide-react": "^0.523.0", "next": "15.1.6", "razorpay": "^2.9.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-hot-toast": "^2.5.1", "react-icons": "^5.5.0", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "stripe": "^17.6.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-slick": "^0.23.13", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}